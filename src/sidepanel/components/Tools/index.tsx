import { building } from '@src/common/images'
import Navbar from '../Navbar'
import { useEffect, useState } from 'react'
import { Card, Input, Button, Spin } from 'antd'

interface ToolsProps {
  selectedText?: string;
  textOperation?: string;
}

export default ({ selectedText, textOperation }: ToolsProps) => {
  const [text, setText] = useState<string>('');
  const [result, setResult] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [operation, setOperation] = useState<string>('');

  // 当选中的文本和操作类型变化时，更新状态
  useEffect(() => {
    if (selectedText) {
      setText(selectedText);
    }
    if (textOperation) {
      setOperation(textOperation);
    }
  }, [selectedText, textOperation]);

  // 处理文本操作
  const handleProcess = () => {
    if (!text) return;
    
    setLoading(true);
    
    // 这里可以根据不同的操作类型调用不同的API
    // 模拟API调用
    setTimeout(() => {
      let processedText = '';
      
      switch (operation || '总结') {
        case '总结':
          processedText = `这是对文本的总结：\n${text.substring(0, 50)}...的主要内容是关于...`;
          break;
        case '翻译':
          processedText = `这是翻译结果：\n${text.split('').reverse().join('')}`;
          break;
        case '缩写':
          processedText = `这是缩写结果：\n${text.substring(0, Math.floor(text.length / 2))}...`;
          break;
        case '扩写':
          processedText = `这是扩写结果：\n${text}\n此外，还可以补充说明...`;
          break;
        case '润色':
          processedText = `这是润色结果：\n${text.replace(/。/g, '。这很重要。')}`;
          break;
        case '修正拼写和语义':
          processedText = `这是修正结果：\n${text.replace(/[a-zA-Z]+/g, word => word.toUpperCase())}`;
          break;
        default:
          processedText = `处理结果：\n${text}`;
      }
      
      setResult(processedText);
      setLoading(false);
    }, 1000);
  };

  // 如果没有选中的文本，显示默认界面
  if (!selectedText && !text) {
    return (
      <div>
        <Navbar hideHistory={true} />
        <div
          style={{
            width: '100%',
            marginTop: '40vh',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          <img style={{ width: '288px' }} src={building} alt="" />
        </div>
      </div>
    );
  }

  return (
    <div>
      <Navbar hideHistory={true} />
      <div style={{ padding: '16px' }}>
        <Card title={operation || '文本处理工具'} style={{ marginBottom: '16px' }}>
          <Input.TextArea
            value={text}
            onChange={(e) => setText(e.target.value)}
            placeholder="请输入要处理的文本"
            autoSize={{ minRows: 4, maxRows: 8 }}
            style={{ marginBottom: '16px' }}
          />

          <div style={{ marginBottom: '16px' }}>
            <Button
              type="primary"
              onClick={handleProcess}
              loading={loading}
              style={{ marginRight: '8px' }}
            >
              {operation || '处理'}
            </Button>

            <Button
              onClick={() => {
                setText('');
                setResult('');
                setOperation('');
              }}
            >
              清空
            </Button>
          </div>

          {loading && (
            <div style={{ textAlign: 'center', padding: '20px' }}>
              <Spin tip="处理中..." />
            </div>
          )}

          {result && !loading && (
            <Card type="inner" title="处理结果">
              <pre style={{ whiteSpace: 'pre-wrap', wordBreak: 'break-word' }}>
                {result}
              </pre>
            </Card>
          )}
        </Card>
      </div>
    </div>
  );
}

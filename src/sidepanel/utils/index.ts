import { isExtensionPage } from '@src/common/utils'
import { EIP_CONFIG } from '@src/common/const'

export const getCurrentTab = async (messageApi) => {
  const tabs = await chrome.tabs.query({ active: true, currentWindow: true })
  const currentTab = tabs[0]
  if (isExtensionPage(currentTab.url)) {
    messageApi.open({
      type: 'error',
      content: '不支持Chrome内置页面，请在其他页面尝试',
    })
    return
  }
  return currentTab
}

// 检查登录状态函数
export const checkLoginStatus = (callback: (isLoggedIn: boolean, data: any) => void) => {
  chrome.cookies.get({ url: EIP_CONFIG.BASE_URL + '/', name: EIP_CONFIG.TOKEN_NAME }, function(cookie) {
    console.log('cookie is ', cookie)
    if (cookie && cookie.value) {
      fetch(EIP_CONFIG.AUTH_API, {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Cookie': `${EIP_CONFIG.TOKEN_NAME}=` + cookie.value
        }
      })
      .then(response => {
        // 获取响应体内容
        return response.json().then(data => {
          console.log('响应体内容:', data);
          return { response, data };
        }).catch(err => {
          console.error('解析响应失败:', err);
          return { response, data: null };
        });
      })
      .then((result) => {
        if (result.response.ok && result.data) {
          console.log('登录验证成功:', result.data)
          callback(true, result.data);
        } else {
          callback(false, null);
        }
      })
      .catch(err => {
        console.error('登录验证失败:', err);
        callback(false, null);
      });
    } else {
      callback(false, null);
    }
  });
}

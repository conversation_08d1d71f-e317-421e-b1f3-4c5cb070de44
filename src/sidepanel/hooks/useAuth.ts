import { useState, useEffect } from 'react'
import { checkLoginStatus } from '../utils'

export const useAuth = () => {
  const [userInfo, setUserInfo] = useState(null)

  useEffect(() => {
    // 页面加载时检查登录状态
    checkLoginStatus((isLoggedIn, data) => {
      if (isLoggedIn) {
        setUserInfo(data)
      }
    })

    // 监听来自 background 的登录成功消息
    const messageListener = (message, sender, sendResponse) => {
      if (message.type === 'loginSuccess') {
        checkLoginStatus((isLoggedIn, data) => {
          if (isLoggedIn) {
            setUserInfo(data)
            console.log('登录成功，更新用户信息:', data)
            // 关闭登录 tab
            if (message.payload && message.payload.tabId) {
              chrome.tabs.remove(message.payload.tabId)
            }
          }
        })
      }
    }

    chrome.runtime.onMessage.addListener(messageListener)

    // 清理监听器
    return () => {
      chrome.runtime.onMessage.removeListener(messageListener)
    }
  }, [])

  return { userInfo, setUserInfo }
}
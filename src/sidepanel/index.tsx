/**
 * 侧边栏功能（Side Panel）是在 Chrome 114 版本后才添加的新功能
 */
import { MemoryRouter } from 'react-router-dom'
import { Provider } from 'react-redux'
import { PersistGate } from '@plasmohq/redux-persist/integration/react'
import { persistor, store } from '@src/store'
import Login from '@src/sidepanel/components/Login'
import App from './App'
import './style.less'
import { useAuth } from './hooks/useAuth'

export default () => {
  const { userInfo, setUserInfo } = useAuth()

  return (
    <Provider store={store}>
      <PersistGate loading={null} persistor={persistor}>
        <MemoryRouter>
          {!userInfo ? <Login setUserInfo={setUserInfo} /> : <App />}
        </MemoryRouter>
      </PersistGate>
    </Provider>
  )
}

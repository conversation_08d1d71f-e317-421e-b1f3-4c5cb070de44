import { useEffect } from 'react'
import { Route, Routes, useNavigate } from 'react-router-dom'
import { useToolType } from '@src/common/hooks'
import { routesConfig } from './config'

interface RoutingProps {
  selectedText?: string;
  textOperation?: string;
}

export default ({ selectedText, textOperation }: RoutingProps) => {
  const navigation = useNavigate()
  const { isTools } = useToolType()

  useEffect(() => {
    // 插件默认初始路由是/，如果是工具tab需要跳转到工具页面
    if (isTools) {
      navigation(routesConfig.tools.path)
    }
  }, [])

  // 当选中文本变化时，可以根据操作类型导航到相应的页面
  useEffect(() => {
    if (selectedText && textOperation) {
      // 根据操作类型决定导航到哪个页面
      switch (textOperation) {
        case '总结':
        case '翻译':
        case '缩写':
        case '扩写':
        case '润色':
        case '修正拼写和语义':
          // 导航到工具页面
          navigation(routesConfig.tools.path);
          break;
        case '继续问':
          // 导航到聊天页面
          navigation(routesConfig.home.path);
          break;
        default:
          break;
      }
    }
  }, [selectedText, textOperation, navigation]);

  return (
    <Routes>
      {Object.values(routesConfig).map(({ path, Component }) => {
        // 将选中的文本和操作类型传递给页面组件
        return (
          <Route 
            key={path} 
            path={path} 
            element={
              <Component 
                selectedText={selectedText} 
                textOperation={textOperation} 
              />
            } 
          />
        )
      })}
    </Routes>
  )
}

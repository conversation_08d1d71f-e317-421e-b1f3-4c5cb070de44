// 菜单项类型定义
export interface MenuItemType {
  id: string;
  label: string;
  action: string;
  title?: string;
  icon?: string;
  type: 'button' | 'icon' | 'close' | 'divider';
  position: 'main' | 'dropdown';
}

// 菜单项配置
export const menuItems: MenuItemType[] = [
  // 主要按钮区域
  {
    id: 'ai-assistant',
    label: '',
    action: 'open-panel',
    title: 'AI助手面板',
    type: 'icon',
    position: 'main',
    icon: 'chatLogo'
  },
  {
    id: 'summary',
    label: '总结',
    action: 'summary',
    type: 'button',
    position: 'main'
  },
  {
    id: 'translate',
    label: '翻译',
    action: 'translate',
    type: 'button',
    position: 'main'
  },
  
  // 下拉菜单项
  {
    id: 'abbreviate',
    label: '缩写',
    action: 'abbreviate',
    type: 'button',
    position: 'dropdown'
  },
  {
    id: 'expand',
    label: '扩写',
    action: 'expand',
    type: 'button',
    position: 'dropdown'
  },
  {
    id: 'polish',
    label: '润色',
    action: 'polish',
    type: 'button',
    position: 'dropdown'
  },
  {
    id: 'correct',
    label: '修正',
    action: 'correct',
    type: 'button',
    position: 'dropdown'
  }
];

// AI处理类操作列表
export const aiActions = ['summary', 'translate', 'abbreviate', 'expand', 'polish', 'correct'];

// 获取主要按钮（显示在工具栏上的按钮）
export const getMainButtons = (): MenuItemType[] => {
  return menuItems.filter(item => item.position === 'main');
};

// 获取下拉菜单项
export const getDropdownItems = (): MenuItemType[] => {
  return menuItems.filter(item => item.position === 'dropdown');
};

// 根据action判断是否为AI处理类操作
export const isAIAction = (action: string): boolean => {
  return aiActions.includes(action);
};

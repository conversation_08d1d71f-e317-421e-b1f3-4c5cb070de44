/**
 * AI 服务测试工具
 */

import { getAIService } from '../config/aiConfig';

/**
 * 测试 AI 服务
 */
export async function testAIService(): Promise<void> {
  console.log('🧪 开始测试 AI 服务...');
  
  const aiService = getAIService();
  const testText = '这是一段测试文本，用于验证AI服务的功能。';
  
  // 测试总结功能
  console.log('📝 测试总结功能...');
  await new Promise<void>((resolve) => {
    aiService.processText(
      { action: 'summary', text: testText },
      (response) => {
        if (response.content) {
          console.log('📄 收到内容:', response.content.slice(0, 50) + '...');
        }
        if (response.isComplete) {
          console.log('✅ 总结完成');
          resolve();
        }
        if (response.error) {
          console.error('❌ 总结失败:', response.error);
          resolve();
        }
      },
      (error) => {
        console.error('❌ 总结错误:', error.message);
        resolve();
      }
    );
  });

  // 测试翻译功能
  console.log('🌐 测试翻译功能...');
  await new Promise<void>((resolve) => {
    aiService.processText(
      { action: 'translate', text: testText },
      (response) => {
        if (response.content) {
          console.log('🔤 收到翻译:', response.content.slice(0, 50) + '...');
        }
        if (response.isComplete) {
          console.log('✅ 翻译完成');
          resolve();
        }
        if (response.error) {
          console.error('❌ 翻译失败:', response.error);
          resolve();
        }
      },
      (error) => {
        console.error('❌ 翻译错误:', error.message);
        resolve();
      }
    );
  });

  console.log('🎉 AI 服务测试完成！');
}

/**
 * 在控制台中运行测试
 * 使用方法：在浏览器控制台中输入 window.testAIService()
 */
if (typeof window !== 'undefined') {
  (window as any).testAIService = testAIService;
}

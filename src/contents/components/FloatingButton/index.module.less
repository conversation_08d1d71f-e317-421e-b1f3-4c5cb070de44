.floatingButton {
  position: fixed;
  z-index: 2147483647;
  user-select: none;

  &.dragging {
    .floatingMain {
      cursor: grabbing;
      transform: scale(1.05);
    }
  }
}

.floatingMain {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: #007bff;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 20px rgba(0, 123, 255, 0.3);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  animation: pulse 3s infinite;

  &:hover {
    transform: scale(1.1);
    box-shadow: 0 8px 25px rgba(0, 123, 255, 0.4);
  }

  &:active {
    transform: scale(0.95);
    transition: all 0.1s cubic-bezier(0.4, 0, 0.2, 1);
  }

  img {
    width: 24px;
    height: 24px;
    filter: brightness(0) invert(1);
    transition: transform 0.2s ease;
  }
}

.floatingMenu {
  position: absolute;
  bottom: 60px;
  right: 0;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  padding: 8px;
  min-width: 150px;
  opacity: 0;
  visibility: hidden;
  transform: translateY(12px) scale(0.92);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0) scale(1);
  }

  // 菜单箭头
  &::after {
    content: '';
    position: absolute;
    bottom: -8px;
    right: 20px;
    width: 0;
    height: 0;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-top: 8px solid white;
    filter: drop-shadow(0 2px 2px rgba(0, 0, 0, 0.1));
  }
}

.floatingMenuItem {
  padding: 10px 12px;
  cursor: pointer;
  border-radius: 6px;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 14px;
  color: #333;

  &:hover {
    background-color: #f8f9fa;
    color: #007bff;
    transform: translateX(2px);
  }

  &:active {
    background-color: #e9ecef;
    transform: translateX(1px);
  }
}

// 脉冲动画
@keyframes pulse {
  0% {
    box-shadow: 0 4px 20px rgba(0, 123, 255, 0.3);
    transform: scale(1);
  }
  50% {
    box-shadow: 0 6px 30px rgba(0, 123, 255, 0.5);
    transform: scale(1.02);
  }
  100% {
    box-shadow: 0 4px 20px rgba(0, 123, 255, 0.3);
    transform: scale(1);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .floatingButton {
    .floatingMain {
      width: 45px;
      height: 45px;

      img {
        width: 20px;
        height: 20px;
      }
    }

    .floatingMenu {
      min-width: 130px;

      .floatingMenuItem {
        padding: 8px 10px;
        font-size: 13px;
      }
    }
  }
}

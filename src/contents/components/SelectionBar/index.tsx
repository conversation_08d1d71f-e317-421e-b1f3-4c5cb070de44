import React, { useState, useEffect } from 'react';
import { getMainButtons, getDropdownItems } from '../../../config/menuItems';
import type { MenuItemType } from '../../../config/menuItems';
import { chatLogo } from '../../../common/images';
import * as styles from "./index.module.less";

// 导入测试工具（仅在开发环境）
if (process.env.NODE_ENV === 'development') {
  import('../../utils/testAIService');
}

// 图标映射
const getIconSrc = (iconName?: string) => {
  switch (iconName) {
    case 'chatLogo':
      return chatLogo;
    default:
      return null;
  }
};

interface SelectionBarProps {
  selectedText: string;
  onAction: (action: string) => void;
  onClose: () => void;
}

const SelectionBar: React.FC<SelectionBarProps> = ({ selectedText, onAction, onClose }) => {
  const [showDropdown, setShowDropdown] = useState(false);

  console.log('SelectionBar render: showDropdown =', showDropdown);

  useEffect(() => {
    console.log('SelectionBar: showDropdown state changed to:', showDropdown);
  }, [showDropdown]);

  const handleAction = (action: string) => {
    // 关闭下拉菜单
    setShowDropdown(false);

    // 所有操作都通过onAction回调传递给父组件处理
    onAction(action);

    // 非面板操作需要关闭SelectionBar
    if (action !== 'open-panel') {
      onClose();
    }
  };

  const toggleDropdown = (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();
    e.nativeEvent.stopImmediatePropagation(); // 阻止原生事件的立即传播
    console.log('Toggling dropdown, current state:', showDropdown, 'will become:', !showDropdown);
    setShowDropdown(!showDropdown);
  };



  // 渲染主要按钮
  const renderMainButton = (item: MenuItemType) => {
    const commonProps = {
      onClick: (e: React.MouseEvent) => {
        e.stopPropagation();
        e.preventDefault();
        handleAction(item.action);
      },
      onMouseDown: (e: React.MouseEvent) => {
        e.stopPropagation();
        e.preventDefault();
      }
    };

    if (item.type === 'icon') {
      const iconSrc = getIconSrc(item.icon);
      return (
        <div
          key={item.id}
          className={styles.selectionIcon}
          title={item.title}
          {...commonProps}
        >
          {iconSrc ? (
            <img src={iconSrc} alt={item.title || 'AI助手'} />
          ) : (
            // 如果没有图标，显示默认的AI图标文字
            <span className={styles.defaultIcon}>AI</span>
          )}
        </div>
      );
    }

    if (item.type === 'button') {
      const iconSrc = getIconSrc(item.icon);
      return (
        <div
          key={item.id}
          className={styles.selectionButton}
          {...commonProps}
        >
          {iconSrc && <img src={iconSrc} alt={item.label} />}
          <span>{item.label}</span>
        </div>
      );
    }

    return null;
  };

  return (
    <>
      <div className={styles.selectionContainer}>
        {/* 箭头指向选中文本 */}
        <div className={styles.selectionArrow} />

        <div className={styles.selectionBar}>
          {/* 渲染主要按钮 */}
          {getMainButtons().map(renderMainButton)}

      <div className={styles.selectionMore}>
        <div
          className={styles.selectionDots}
          onClick={toggleDropdown}
          onMouseDown={(e) => {
            e.stopPropagation();
            e.preventDefault();
          }}
          onMouseUp={(e) => {
            e.stopPropagation();
            e.preventDefault();
          }}
        >
          ⋮
        </div>
        {showDropdown && (
          <div
            className={`${styles.selectionDropdown} ${showDropdown ? styles.show : ''}`}
            onClick={(e) => e.stopPropagation()}
          >
            {getDropdownItems().map((item) => (
              <div
                key={item.id}
                className={styles.selectionDropdownItem}
                onClick={(e) => {
                  e.stopPropagation();
                  handleAction(item.action);
                }}
              >
                {item.label}
              </div>
            ))}
          </div>
        )}
      </div>

      {/* 分隔线 */}
      <div className={styles.selectionDivider} />

      <div
        className={styles.selectionClose}
        onClick={(e) => {
          e.stopPropagation();
          e.preventDefault();
          onClose();
        }}
        onMouseDown={(e) => {
          e.stopPropagation();
          e.preventDefault();
        }}
        title="关闭"
      >
        ×
        </div>
        </div>
      </div>
    </>
  );
};

export default SelectionBar;

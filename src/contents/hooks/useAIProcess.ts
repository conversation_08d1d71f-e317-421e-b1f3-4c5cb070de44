/**
 * AI 处理 Hook
 */

import { useState, useCallback, useRef, useEffect } from 'react';
import { getAIService } from '../config/aiConfig';
import { AIRequest, AIStreamResponse } from '../services/aiService';

export interface AIProcessState {
  isProcessing: boolean;
  content: string;
  isComplete: boolean;
  error: string | null;
  processingTime: number;
}

export interface UseAIProcessReturn {
  state: AIProcessState;
  startProcess: (action: string, text: string, options?: Record<string, any>) => void;
  stopProcess: () => void;
  resetState: () => void;
}

/**
 * AI 处理 Hook
 */
export function useAIProcess(): UseAIProcessReturn {
  const [state, setState] = useState<AIProcessState>({
    isProcessing: false,
    content: '',
    isComplete: false,
    error: null,
    processingTime: 0,
  });

  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);
  const aiServiceRef = useRef(getAIService());

  // 清理定时器
  const clearTimer = useCallback(() => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }
  }, []);

  // 开始处理
  const startProcess = useCallback(async (
    action: string,
    text: string,
    options?: Record<string, any>
  ) => {
    // 重置状态
    setState({
      isProcessing: true,
      content: '',
      isComplete: false,
      error: null,
      processingTime: 0,
    });

    // 启动计时器
    timerRef.current = setInterval(() => {
      setState(prev => ({
        ...prev,
        processingTime: prev.processingTime + 1
      }));
    }, 1000);

    // 创建请求
    const request: AIRequest = {
      action,
      text,
      options,
    };

    // 处理流式响应
    const handleStream = (response: AIStreamResponse) => {
      setState(prev => {
        if (response.error) {
          return {
            ...prev,
            isProcessing: false,
            error: response.error,
            isComplete: true,
          };
        }

        if (response.isComplete) {
          return {
            ...prev,
            isProcessing: false,
            isComplete: true,
          };
        }

        return {
          ...prev,
          content: prev.content + response.content,
        };
      });
    };

    // 处理错误
    const handleError = (error: Error) => {
      setState(prev => ({
        ...prev,
        isProcessing: false,
        error: error.message,
        isComplete: true,
      }));
    };

    try {
      await aiServiceRef.current.processText(request, handleStream, handleError);
    } catch (error) {
      handleError(error as Error);
    } finally {
      clearTimer();
    }
  }, [clearTimer]);

  // 停止处理
  const stopProcess = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    
    setState(prev => ({
      ...prev,
      isProcessing: false,
      isComplete: true,
    }));
    
    clearTimer();
  }, [clearTimer]);

  // 重置状态
  const resetState = useCallback(() => {
    setState({
      isProcessing: false,
      content: '',
      isComplete: false,
      error: null,
      processingTime: 0,
    });
    clearTimer();
  }, [clearTimer]);

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      clearTimer();
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, [clearTimer]);

  return {
    state,
    startProcess,
    stopProcess,
    resetState,
  };
}

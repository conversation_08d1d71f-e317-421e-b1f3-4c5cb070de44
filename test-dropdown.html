<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>下拉菜单测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            padding: 50px;
            background-color: #f5f5f5;
        }
        
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .selection-bar {
            display: flex;
            align-items: center;
            background-color: white;
            border-radius: 20px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
            padding: 8px 12px;
            border: 1px solid #ddd;
            width: fit-content;
            margin: 20px 0;
        }
        
        .selection-button {
            display: flex;
            align-items: center;
            margin: 0 6px;
            cursor: pointer;
            color: #1c1c1e;
            padding: 4px 8px;
            border-radius: 4px;
            transition: all 0.2s ease;
        }
        
        .selection-button:hover {
            background-color: #f0f0f0;
        }
        
        .selection-more {
            position: relative;
            display: flex;
            align-items: center;
            margin: 0 6px;
            cursor: pointer;
        }
        
        .selection-dots {
            font-size: 16px;
            color: #1c1c1e;
            padding: 4px 8px;
            border-radius: 4px;
            transition: all 0.2s ease;
        }
        
        .selection-dots:hover {
            background-color: #f0f0f0;
        }
        
        .selection-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            background-color: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
            border-radius: 4px;
            min-width: 120px;
            z-index: 10000;
            border: 1px solid #ddd;
            margin-top: 4px;
        }
        
        .selection-dropdown-item {
            padding: 8px 12px;
            cursor: pointer;
            transition: background-color 0.2s ease;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .selection-dropdown-item:last-child {
            border-bottom: none;
        }
        
        .selection-dropdown-item:hover {
            background-color: #f0f0f0;
            color: #007bff;
        }
        
        .selection-dropdown-item:first-child {
            border-top-left-radius: 4px;
            border-top-right-radius: 4px;
        }
        
        .selection-dropdown-item:last-child {
            border-bottom-left-radius: 4px;
            border-bottom-right-radius: 4px;
        }
        
        .debug-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
            font-family: monospace;
            font-size: 14px;
        }
        
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        
        .status.hidden {
            background: #f8d7da;
            color: #721c24;
        }
        
        .status.visible {
            background: #d4edda;
            color: #155724;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 下拉菜单独立测试</h1>
        <p>这是一个独立的下拉菜单测试，用来验证基本的HTML/CSS/JS功能。</p>
        
        <div class="selection-bar">
            <div class="selection-button">AI</div>
            <div class="selection-button">总结</div>
            <div class="selection-button">翻译</div>
            
            <div class="selection-more">
                <div class="selection-dots" onclick="toggleDropdown()">⋮</div>
                <div id="dropdown" class="selection-dropdown" style="display: none;">
                    <div class="selection-dropdown-item" onclick="selectItem('缩写')">缩写</div>
                    <div class="selection-dropdown-item" onclick="selectItem('扩写')">扩写</div>
                    <div class="selection-dropdown-item" onclick="selectItem('润色')">润色</div>
                    <div class="selection-dropdown-item" onclick="selectItem('修正')">修正</div>
                </div>
            </div>
            
            <div class="selection-button">×</div>
        </div>
        
        <div class="debug-info">
            <div><strong>调试信息：</strong></div>
            <div id="status" class="status hidden">下拉菜单状态: 隐藏</div>
            <div>点击次数: <span id="clickCount">0</span></div>
            <div>最后操作: <span id="lastAction">无</span></div>
        </div>
        
        <div>
            <h3>测试步骤：</h3>
            <ol>
                <li>点击三个点(⋮)按钮</li>
                <li>观察下拉菜单是否出现</li>
                <li>点击菜单项测试功能</li>
                <li>点击外部区域测试关闭功能</li>
            </ol>
        </div>
        
        <div>
            <h3>如果这个测试正常工作，说明问题在于：</h3>
            <ul>
                <li>React组件的状态管理</li>
                <li>Shadow DOM中的样式隔离</li>
                <li>事件处理冲突</li>
                <li>CSS类名处理问题</li>
            </ul>
        </div>
    </div>

    <script>
        let isDropdownVisible = false;
        let clickCount = 0;
        
        function toggleDropdown() {
            clickCount++;
            document.getElementById('clickCount').textContent = clickCount;
            
            const dropdown = document.getElementById('dropdown');
            const status = document.getElementById('status');
            const lastAction = document.getElementById('lastAction');
            
            isDropdownVisible = !isDropdownVisible;
            
            if (isDropdownVisible) {
                dropdown.style.display = 'block';
                status.textContent = '下拉菜单状态: 显示';
                status.className = 'status visible';
                lastAction.textContent = '显示下拉菜单';
            } else {
                dropdown.style.display = 'none';
                status.textContent = '下拉菜单状态: 隐藏';
                status.className = 'status hidden';
                lastAction.textContent = '隐藏下拉菜单';
            }
            
            console.log('Dropdown toggled:', isDropdownVisible);
        }
        
        function selectItem(item) {
            const lastAction = document.getElementById('lastAction');
            lastAction.textContent = '选择了: ' + item;
            
            // 隐藏下拉菜单
            const dropdown = document.getElementById('dropdown');
            const status = document.getElementById('status');
            dropdown.style.display = 'none';
            status.textContent = '下拉菜单状态: 隐藏';
            status.className = 'status hidden';
            isDropdownVisible = false;
            
            console.log('Item selected:', item);
            alert('选择了: ' + item);
        }
        
        // 点击外部关闭下拉菜单
        document.addEventListener('click', function(e) {
            const dropdown = document.getElementById('dropdown');
            const selectionMore = e.target.closest('.selection-more');
            
            if (!selectionMore && isDropdownVisible) {
                dropdown.style.display = 'none';
                document.getElementById('status').textContent = '下拉菜单状态: 隐藏';
                document.getElementById('status').className = 'status hidden';
                document.getElementById('lastAction').textContent = '点击外部关闭';
                isDropdownVisible = false;
                console.log('Dropdown closed by outside click');
            }
        });
    </script>
</body>
</html>
